<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.hnyiti.cloud</groupId>
    <artifactId>hnyiti-framework</artifactId>
    <version>2.0.0</version>
  </parent>
  <groupId>com.hnyiti.cloud</groupId>
  <artifactId>hnyiti-spring-boot-starter-biz-data-permission</artifactId>
  <version>2.0.0</version>
  <name>${project.artifactId}</name>
  <description>数据权限</description>
  <dependencies>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-spring-boot-starter-security</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-spring-boot-starter-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-module-system-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
