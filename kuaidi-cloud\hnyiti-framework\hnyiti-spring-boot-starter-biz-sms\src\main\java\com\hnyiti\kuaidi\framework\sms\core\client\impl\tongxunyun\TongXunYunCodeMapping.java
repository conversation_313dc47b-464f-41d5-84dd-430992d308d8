package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.hnyiti.kuaidi.framework.sms.core.client.SmsCodeMapping;
import com.hnyiti.kuaidi.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;

/**
 * 通讯云 SmsCodeMapping 实现类
 *
 * <AUTHOR>
 */
public class TongXunYunCodeMapping implements SmsCodeMapping {

    @Override
    public ErrorCode apply(String apiCode) {
        switch (apiCode) {
            case "0":
                return GlobalErrorCodeConstants.SUCCESS;
            case "1":
                return SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_MONEY_NOT_ENOUGH;
            case "2":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "3":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "4":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            case "5":
                return SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID;
            case "6":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "7":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "8":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            case "9":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "10":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "11":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            case "12":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "13":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "14":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            case "15":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "16":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "17":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            case "18":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID;
            case "19":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID;
            case "20":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL;
            default:
                return SmsFrameworkErrorCodeConstants.SMS_UNKNOWN;
        }
    }

}
