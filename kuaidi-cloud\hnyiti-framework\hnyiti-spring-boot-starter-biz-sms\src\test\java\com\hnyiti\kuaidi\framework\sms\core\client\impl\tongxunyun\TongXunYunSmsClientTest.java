package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import com.hnyiti.kuaidi.framework.common.core.KeyValue;
import com.hnyiti.kuaidi.framework.sms.core.client.SmsCommonResult;
import com.hnyiti.kuaidi.framework.sms.core.client.dto.SmsSendRespDTO;
import com.hnyiti.kuaidi.framework.sms.core.enums.SmsChannelEnum;
import com.hnyiti.kuaidi.framework.sms.core.property.SmsChannelProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * {@link TongXunYunSmsClient} 的单元测试
 */
public class TongXunYunSmsClientTest {

    private TongXunYunSmsClient smsClient;

    @BeforeEach
    public void setUp() {
        // 创建配置类
        SmsChannelProperties properties = new SmsChannelProperties();
        properties.setId(1L);
        properties.setSignature("测试签名");
        properties.setCode(SmsChannelEnum.TONGXUNYUN.getCode());
        properties.setApiKey("test_account");
        properties.setApiSecret("test_password");
        
        // 创建客户端
        smsClient = new TongXunYunSmsClient(properties);
        smsClient.init();
    }

    @Test
    public void testSendSms() {
        // 准备参数
        Long sendLogId = 1L;
        String mobile = "***********";
        String apiTemplateId = "test_template";
        
        List<KeyValue<String, Object>> templateParams = new ArrayList<>();
        templateParams.add(new KeyValue<>("content", "您的验证码是：123456"));
        
        // 注意：这个测试需要真实的账号和密码才能成功
        // 这里只是验证代码结构是否正确
        try {
            SmsCommonResult<SmsSendRespDTO> result = smsClient.sendSms(sendLogId, mobile, apiTemplateId, templateParams);
            System.out.println("发送结果: " + result);
        } catch (Exception e) {
            System.out.println("发送异常（预期的，因为使用的是测试账号）: " + e.getMessage());
        }
    }
}
