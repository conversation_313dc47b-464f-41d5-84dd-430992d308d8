package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import com.hnyiti.kuaidi.framework.common.core.KeyValue;
import com.hnyiti.kuaidi.framework.sms.core.client.SmsCommonResult;
import com.hnyiti.kuaidi.framework.sms.core.client.dto.SmsSendRespDTO;
import com.hnyiti.kuaidi.framework.sms.core.enums.SmsChannelEnum;
import com.hnyiti.kuaidi.framework.sms.core.property.SmsChannelProperties;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * {@link TongXunYunSmsClient} 的集成测试
 * 
 * 使用说明：
 * 1. 将 apiKey 设置为通讯云提供的账号
 * 2. 将 apiSecret 设置为通讯云提供的密码（MD5加密后的32位小写）
 * 3. 运行测试验证短信发送功能
 */
public class TongXunYunSmsClientIntegrationTest {

    private static TongXunYunSmsClient smsClient;

    @BeforeAll
    public static void init() {
        // 创建配置类
        SmsChannelProperties properties = new SmsChannelProperties();
        properties.setId(1L);
        properties.setSignature("测试签名");
        properties.setCode(SmsChannelEnum.TONGXUNYUN.getCode());
        
        // TODO: 替换为真实的通讯云账号和密码
        properties.setApiKey("your_account_here");
        properties.setApiSecret("your_md5_password_here");
        
        // 创建客户端
        smsClient = new TongXunYunSmsClient(properties);
        smsClient.init();
    }

    @Test
    public void testSendSms() {
        // 准备参数
        Long sendLogId = System.currentTimeMillis();
        String mobile = "***********"; // 替换为真实手机号
        String apiTemplateId = "test_template";
        
        List<KeyValue<String, Object>> templateParams = new ArrayList<>();
        templateParams.add(new KeyValue<>("content", "您的验证码是：123456，请在5分钟内使用。"));
        
        // 发送短信
        SmsCommonResult<SmsSendRespDTO> result = smsClient.sendSms(sendLogId, mobile, apiTemplateId, templateParams);
        
        // 打印结果
        System.out.println("发送结果: " + result);
        System.out.println("是否成功: " + result.isSuccess());
        System.out.println("API返回码: " + result.getApiCode());
        System.out.println("API返回消息: " + result.getApiMsg());
        
        if (result.getData() != null) {
            System.out.println("序列号: " + result.getData().getSerialNo());
        }
    }

    /**
     * 测试不同的短信内容
     */
    @Test
    public void testSendSmsWithDifferentContent() {
        List<String> contents = List.of(
            "您的验证码是：123456",
            "您的订单已发货，快递单号：SF1234567890",
            "您的账户余额不足，请及时充值"
        );
        
        for (int i = 0; i < contents.size(); i++) {
            Long sendLogId = System.currentTimeMillis() + i;
            String mobile = "***********";
            String apiTemplateId = "test_template_" + i;
            
            List<KeyValue<String, Object>> templateParams = new ArrayList<>();
            templateParams.add(new KeyValue<>("content", contents.get(i)));
            
            SmsCommonResult<SmsSendRespDTO> result = smsClient.sendSms(sendLogId, mobile, apiTemplateId, templateParams);
            System.out.println("内容 " + (i + 1) + " 发送结果: " + result.getApiCode() + " - " + result.getApiMsg());
        }
    }
}
