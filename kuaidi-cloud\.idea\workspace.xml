<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b88b1940-47bc-46d2-9be9-7b9f14e5040d" name="Changes" comment="fix(system): 修复 admin 用户登录导致的 SQL 注入和数据泄露问题&#10;&#10;- 在登录前清除租户上下文，避免数据泄露- 更新登录日志配置，忽略 system_users 表的操作记录">
      <change beforePath="$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-sms/src/main/java/com/hnyiti/kuaidi/framework/sms/core/client/impl/SmsClientFactoryImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-sms/src/main/java/com/hnyiti/kuaidi/framework/sms/core/client/impl/SmsClientFactoryImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-sms/src/main/java/com/hnyiti/kuaidi/framework/sms/core/enums/SmsChannelEnum.java" beforeDir="false" afterPath="$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-sms/src/main/java/com/hnyiti/kuaidi/framework/sms/core/enums/SmsChannelEnum.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="pydev &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="develop-member" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/cn/hutool/hutool-all/5.8.18/hutool-all-5.8.18.jar!/cn/hutool/core/date/DatePattern.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/cn/hutool/hutool-all/5.8.18/hutool-all-5.8.18.jar!/cn/hutool/core/date/DateTime.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/cn/hutool/hutool-all/5.8.18/hutool-all-5.8.18.jar!/cn/hutool/core/date/DateUtil.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-annotation/*******/mybatis-plus-annotation-*******.jar!/com/baomidou/mybatisplus/annotation/IdType.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-annotation/*******/mybatis-plus-annotation-*******.jar!/com/baomidou/mybatisplus/annotation/TableId.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar!/com/baomidou/mybatisplus/annotation/DbType.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-core/*******/mybatis-plus-core-*******.jar!/com/baomidou/mybatisplus/core/override/MybatisMapperProxy.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-extension/*******/mybatis-plus-extension-*******.jar!/com/baomidou/mybatisplus/extension/plugins/pagination/DialectFactory.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar!/com/fasterxml/jackson/datatype/jsr310/PackageVersion.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/github/gavlyukovskiy/datasource-decorator-spring-boot-autoconfigure/1.11.0/datasource-decorator-spring-boot-autoconfigure-1.11.0.jar!/com/github/gavlyukovskiy/boot/jdbc/decorator/p6spy/P6SpyProperties.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/hnyiti/cloud/hnyiti-spring-boot-starter-mybatis/2.0.0/hnyiti-spring-boot-starter-mybatis-2.0.0.jar!/com/hnyiti/kuaidi/framework/mybatis/core/handler/DefaultDBFieldHandler.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/io/seata/seata-all/1.7.0/seata-all-1.7.0-sources.jar!/io/seata/rm/datasource/sql/struct/cache/AbstractTableMetaCache.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/io/seata/seata-all/1.7.0/seata-all-1.7.0-sources.jar!/io/seata/rm/datasource/sql/struct/cache/PostgresqlTableMetaCache.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-infra-context/5.1.1/shardingsphere-infra-context-5.1.1.jar!/org/apache/shardingsphere/infra/context/kernel/KernelProcessor.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/algorithm/sharding/datetime/IntervalShardingAlgorithm.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/route/engine/ShardingSQLRouter.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/route/engine/condition/ShardingConditions.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/route/engine/type/standard/ShardingStandardRoutingEngine.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/rule/TableRule.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/mybatis/mybatis-spring/2.0.7/mybatis-spring-2.0.7-sources.jar!/org/mybatis/spring/MyBatisExceptionTranslator.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/mybatis/mybatis-spring/2.0.7/mybatis-spring-2.0.7-sources.jar!/org/mybatis/spring/SqlSessionTemplate.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/mybatis/mybatis/3.5.10/mybatis-3.5.10-sources.jar!/org/apache/ibatis/executor/statement/PreparedStatementHandler.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/postgresql/postgresql/42.3.8/postgresql-42.3.8.jar!/org/postgresql/jdbc/PgResultSet.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-jdbc/5.3.27/spring-jdbc-5.3.27.jar!/org/springframework/jdbc/support/SQLStateSQLExceptionTranslator.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-tx/5.3.27/spring-tx-5.3.27.jar!/org/springframework/transaction/annotation/Transactional.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-web/5.3.27/spring-web-5.3.27-sources.jar!/org/springframework/web/context/request/RequestAttributes.java" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files/Java/jdk1.8.0_202/jre/lib/rt.jar!/sun/misc/Unsafe.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files/Java/jdk1.8.0_202/src.zip!/java/time/LocalDateTime.java" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files/Java/jdk1.8.0_202/src.zip!/java/time/format/DateTimeFormatter.java" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/Program Files/Java/jdk1.8.0_202/src.zip!/java/util/Map.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="E:\Code\kuaidi-cloud\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2svtkXxrfEG4eaQ8pioSEwaAdDn" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.CoredataServerApplication | #1.executor": "Run",
    "HTTP Request.CoredataServerApplication | #3.executor": "Run",
    "HTTP Request.CoredataServerApplication | #4.executor": "Run",
    "HTTP Request.KuaiDiServerApplication | #1.executor": "Run",
    "HTTP 请求.CoredataServerApplication | #4.executor": "Run",
    "HTTP 请求.MessageServerApplication | #1.executor": "Run",
    "Maven.hnyiti [clean].executor": "Run",
    "Maven.hnyiti [compile].executor": "Run",
    "Maven.hnyiti [install].executor": "Run",
    "Maven.hnyiti [org.apache.maven.plugins:maven-clean-plugin:3.2.0:clean].executor": "Run",
    "Maven.hnyiti [org.apache.maven.plugins:maven-install-plugin:3.1.2:help].executor": "Run",
    "Maven.hnyiti [org.apache.maven.plugins:maven-install-plugin:3.1.2:install].executor": "Run",
    "Maven.hnyiti-module-drp-kuaidi-api [install].executor": "Run",
    "Maven.hnyiti-module-message [clean].executor": "Run",
    "Maven.hnyiti-module-message [install].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.CallbackServerApplication.executor": "Run",
    "Spring Boot.CoredataServerApplication (1).executor": "Run",
    "Spring Boot.CoredataServerApplication.executor": "Debug",
    "Spring Boot.GatewayServerApplication.executor": "Run",
    "Spring Boot.KuaiDiServerApplication.executor": "Debug",
    "Spring Boot.MessageServerApplication (1).executor": "Run",
    "Spring Boot.MessageServerApplication.executor": "Run",
    "Spring Boot.MonitorApplication.executor": "Debug",
    "Spring Boot.PgsqlServerApplication.executor": "Run",
    "Spring Boot.SystemServerApplication.executor": "Debug",
    "git-widget-placeholder": "develop-plsql",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/Code/kuaidi-all/kuaidi-cloud/hnyiti-module-pgsql/hnyiti-module-pgsql-biz/src/main/java/com/hnyiti/kuaidi/module/pgsql/config",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Code\kuaidi-all\kuaidi-cloud\hnyiti-module-pgsql\hnyiti-module-pgsql-biz\src\main\java\com\hnyiti\kuaidi\module\pgsql\config" />
      <recent name="E:\Code\kuaidi-all\kuaidi-cloud\hnyiti-module-pgsql\hnyiti-module-pgsql-biz\src\main\java\com\hnyiti\kuaidi\module\pgsql" />
      <recent name="E:\Code\kuaidi-all\kuaidi-cloud\hnyiti-module-pgsql" />
      <recent name="E:\Code\kuaidi-all\kuaidi-cloud" />
      <recent name="E:\Code\kuaidi-all\kuaidi-cloud\hnyiti-module-coredata\src\main\resources" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.hnyiti.kuaidi.module.common" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.hnyiti.kuaidi.module.pgsql.config" />
      <recent name="com.hnyiti.kuaidi.module.common.config" />
      <recent name="com.hnyiti.kuaidi.module.config" />
      <recent name="com.hnyiti.kuaidi.module.delivery.config" />
      <recent name="com.hnyiti.kuaidi.module.common.consumer" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SystemServerApplication">
    <configuration name="CoredataServerApplication | #4" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/CoredataServerApplication.http" index="4">
      <method v="2" />
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="kuaidi-cloud" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="CallbackServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-drp-kuaidi-callback" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.CallbackServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CoredataServerApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="hnyiti-module-coredata" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.CoredataServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CoredataServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-coredata" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.common.CoredataServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.gateway.GatewayServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KuaiDiServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-drp-kuaidi-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.KuaiDiServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MessageServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-message-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.message.MessageServerApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hnyiti.kuaidi.module.message.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-monitor" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.monitor.MonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PgsqlServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-pgsql-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.pgsql.PgsqlServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SystemServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_HOST" />
          <option name="value" value="*************" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="NACOS_NAMESPACE" />
          <option name="value" value="pydev2" />
        </param>
      </additionalParameters>
      <module name="hnyiti-module-system-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hnyiti.kuaidi.module.SystemServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP 请求.CoredataServerApplication | #4" />
      <item itemvalue="Spring Boot.CoredataServerApplication (1)" />
      <item itemvalue="Spring Boot.CallbackServerApplication" />
      <item itemvalue="Spring Boot.CoredataServerApplication" />
      <item itemvalue="Spring Boot.GatewayServerApplication" />
      <item itemvalue="Spring Boot.KuaiDiServerApplication" />
      <item itemvalue="Spring Boot.MessageServerApplication" />
      <item itemvalue="Spring Boot.MonitorApplication" />
      <item itemvalue="Spring Boot.PgsqlServerApplication" />
      <item itemvalue="Spring Boot.SystemServerApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP 请求.CoredataServerApplication | #4" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b88b1940-47bc-46d2-9be9-7b9f14e5040d" name="Changes" comment="" />
      <created>1739348020038</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1739348020038</updated>
      <workItem from="1739348021460" duration="13000" />
      <workItem from="1739348043089" duration="3287000" />
      <workItem from="1739364643523" duration="33891000" />
      <workItem from="1739627766274" duration="56515000" />
      <workItem from="1739856628666" duration="38971000" />
      <workItem from="1739974162979" duration="22126000" />
      <workItem from="1740122260014" duration="285000" />
      <workItem from="1740122554873" duration="213000" />
      <workItem from="1740122788836" duration="6848000" />
      <workItem from="1740129866006" duration="8947000" />
      <workItem from="1740376263580" duration="2499000" />
      <workItem from="1740378789771" duration="34823000" />
      <workItem from="1740973738747" duration="921000" />
      <workItem from="1742823928388" duration="757000" />
      <workItem from="1742824774038" duration="13000" />
      <workItem from="1742824828605" duration="29045000" />
      <workItem from="1742906464910" duration="903000" />
      <workItem from="1742907375780" duration="9183000" />
      <workItem from="1743130274003" duration="8625000" />
      <workItem from="1744030959694" duration="45115000" />
      <workItem from="1744192978654" duration="37863000" />
      <workItem from="1744984823436" duration="40772000" />
      <workItem from="1745143929709" duration="14079000" />
      <workItem from="1745161970280" duration="6929000" />
      <workItem from="1745201784791" duration="240000" />
      <workItem from="1745208043782" duration="31900000" />
      <workItem from="1745808414140" duration="2888000" />
      <workItem from="1745829143831" duration="7000" />
      <workItem from="1746617530917" duration="8106000" />
      <workItem from="1747041532997" duration="58000" />
      <workItem from="1747041601443" duration="8670000" />
      <workItem from="1747052599969" duration="13288000" />
      <workItem from="1747224291250" duration="9311000" />
      <workItem from="1747291724012" duration="3974000" />
      <workItem from="1747464495074" duration="160000" />
      <workItem from="1747464676373" duration="19625000" />
      <workItem from="1747895040158" duration="2256000" />
      <workItem from="1747897767465" duration="406000" />
      <workItem from="1747899487103" duration="124404000" />
      <workItem from="1748248582606" duration="628000" />
      <workItem from="1748249337648" duration="2924000" />
      <workItem from="1748419735874" duration="9542000" />
    </task>
    <task id="LOCAL-00001" summary="```&#10;refactor(消息模块): 优化消息数据模型&#10;&#10;1. 新增messages表主键生成策略&#10;2. 更新消息表字段注释&#10;3. 新增消息内容表&#10;4. 配置分表规则&#10;5. 创建每月分片表&#10;6. 新增分区键约束&#10;7. 优化database配置参数&#10;&#10;无相关 issue&#10;```">
      <option name="closed" value="true" />
      <created>1740123168510</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1740123168510</updated>
    </task>
    <task id="LOCAL-00002" summary="```&#10;feat(消息服务): 优化消息服务分表处理及增加新功能&#10;&#10;1. 新增MessageMapper中分表查询和更新方法&#10;2. 优化MessageServiceImpl中的分表处理和查询逻辑&#10;3. 增加MessagePageReqVO新的查询条件字段&#10;4. 更新MessageService接口中的方法定义&#10;5. 移除不必要的组件引用和逻辑&#10;&#10;无相关 issue&#10;```">
      <option name="closed" value="true" />
      <created>1740124530814</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1740124530814</updated>
    </task>
    <task id="LOCAL-00003" summary="refactor(message): 优化分表策略&#10;&#10;1. 修改MessageMapper.xml中的查询和更新条件&#10;   - 将create_time匹配改为DATE_FORMAT(create_time, '%Y-%m')匹配&#10;   - 更新select和update语句中的WHERE条件&#10;&#10;2. 修改MessageTableUtil.java中的分表策略实现&#10;   - 更新getShardingTimeByMessageId方法返回值&#10;   - 只返回年月信息，不再包含具体日期和时间&#10;&#10;issues">
      <option name="closed" value="true" />
      <created>1740125229786</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1740125229786</updated>
    </task>
    <task id="LOCAL-00004" summary="```&#10;feat: 新增消息补偿定时任务&#10;&#10;1. 新增消息补偿定时任务类MessageCompensateJob&#10;2. 实现每3分钟执行一次的消息重试机制&#10;3. 添加重试策略，包括重试次数限制和递增延迟&#10;4. 支持不同消息渠道的发送策略&#10;5. 支持最多处理100条消息&#10;6. 添加详细日志记录和异常处理&#10;&#10;No related issues&#10;```">
      <option name="closed" value="true" />
      <created>1740126498292</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1740126498292</updated>
    </task>
    <task id="LOCAL-00005" summary="```&#10;refactor(消息补偿): 优化消息补偿功能实现&#10;&#10;1. 更新MessageCompensateJob类&#10;   - 删除不必要的导入&#10;   - 移除DateTimeFormatter常量&#10;   - 更新execute()方法实现，使用messageMapper.selectMessagesForCompensate替代手动查询&#10;&#10;2. 更新MessageMapper类&#10;   - 添加selectMessagesForCompensate方法&#10;   - 在MessageMapper.xml中实现selectMessagesForCompensate的.MyBatis映射&#10;&#10;3. 优化日志格式化&#10;   - 保持错误日志的格式化方式不变&#10;&#10;无相关 issue&#10;```">
      <option name="closed" value="true" />
      <created>1740129897812</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1740129897812</updated>
    </task>
    <task id="LOCAL-00006">
      <option name="closed" value="true" />
      <created>1740144420706</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1740144420706</updated>
    </task>
    <task id="LOCAL-00007" summary="```&#10;refactor(message模块): 优化消息分表策略&#10;1. 添加消息更新逻辑到Controller&#10;2. 修改 Mapper中的分表UPDATE语句&#10;3. 调整MessageTableUtil的年月格式处理&#10;```">
      <option name="closed" value="true" />
      <created>1740145979862</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1740145979862</updated>
    </task>
    <task id="LOCAL-00008" summary="```&#10;feat(数据库与业务逻辑): 新增LocalDateTime类型处理与时间范围查询优化&#10;&#10;1. 新增LocalDateTimeTypeHandler处理类，实现LocalDateTime类型与数据库时间格式的自动转换&#10;2. 优化时间范围查询逻辑：&#10;   - 支持默认时间范围（结束时间默认当前时间，开始时间默认一个月前）&#10;   - 增加时间范围超一个月的限制&#10;   - 统一时间格式处理&#10;&#10;无相关 issue&#10;```">
      <option name="closed" value="true" />
      <created>1740148056516</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1740148056516</updated>
    </task>
    <task id="LOCAL-00009" summary="feat(monitor): 更新企业微信机器人配置并添加 Docker 支持- 将单个机器人 key配置改为支持多个 key 配置&#10;- 添加 Dockerfile 以支持容器化部署&#10;- 更新 Nacos 命名空间配置- 重构 ServiceStatusChangeListener 类以支持新配置&#10;- 删除旧的 WxCpProperties 类，创建新的 WxCpRobotProperties 类">
      <option name="closed" value="true" />
      <created>1740380991855</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1740380991855</updated>
    </task>
    <task id="LOCAL-00010" summary="feat(module-message): 实现消息表自动分片功能&#10;&#10;- 新增消息表和消息内容表的分片创建逻辑&#10;- 实现消息内容的独立存储和关联&#10;- 添加定时任务，每天自动检查并创建未来三个月的分片表- 优化消息查询逻辑，支持分片查询">
      <option name="closed" value="true" />
      <created>1740390535814</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1740390535814</updated>
    </task>
    <task id="LOCAL-00011" summary="feat(message): 消息内容库按时间分片&#10;&#10;- 新增 MessageContentMapper 接口的分片方法&#10;- 创建 MessageContentMapper.xml 文件实现分片操作&#10;- 添加 MessageContentTableUtil 工具类用于分片表名生成- 修改 MessageServiceImpl 类，支持消息内容的分片保存和查询- 更新 application.yml，移除不必要的配置项">
      <option name="closed" value="true" />
      <created>1740395365741</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1740395365741</updated>
    </task>
    <task id="LOCAL-00012" summary="refactor(message): 优化消息服务中的分片操作&#10;&#10;- 移除了不必要的分片时间获取操作- 替换 insertWithSharding 方法为普通的 insert 方法&#10;- 在需要时，使用 updateByIdWithSharding 方法进行更新操作&#10;-保留了原有的业务逻辑，仅优化了数据库操作">
      <option name="closed" value="true" />
      <created>1740396190804</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1740396190804</updated>
    </task>
    <task id="LOCAL-00013" summary="refactor(message): 重构消息模块配置&#10;&#10;- 移除 application.yml 文件，所有配置迁移到 Nacos&#10;- 更新 bootstrap.yml 文件，调整 Nacos 服务器地址和命名空间&#10;- 修改 seata.conf 文件，使用环境变量替代硬编码的 Seata 服务器地址">
      <option name="closed" value="true" />
      <created>1740398479008</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1740398479008</updated>
    </task>
    <task id="LOCAL-00014" summary="refactor(monitor): 重构服务状态变更通知功能&#10;&#10;- 移除了 WxCpRobotProperties 类，直接使用配置属性&#10;- 优化了服务状态变更通知的逻辑，支持多种状态&#10;- 添加了对服务下线事件的处理&#10;- 改进了通知消息的格式，使用 Markdown 格式&#10;-集成了 Hutool 工具库，用于 HTTP 请求和 JSON 处理">
      <option name="closed" value="true" />
      <created>1740403804629</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1740403804629</updated>
    </task>
    <task id="LOCAL-00015" summary="refactor: 移除 WxCpRobotService 类&#10;&#10;删除了 hnyiti-monitor 项目中的 WxCpRobotService 类。这个类提供了向企业微信机器人发送消息的功能，包括随机选择 webhook key 实现负载均衡。删除这个类可能是因为不再需要这个功能或者有其他替代方案。">
      <option name="closed" value="true" />
      <created>1740403932132</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1740403932132</updated>
    </task>
    <task id="LOCAL-00016" summary="build(hnyiti-monitor): 移除 Arthas 相关代码&#10;&#10;- 删除了安装 Arthas 的步骤&#10;- 移除了 Arthas 相关的端口暴露&#10;- 添加了直接启动应用的 ENTRYPOINT 指令">
      <option name="closed" value="true" />
      <created>1740404072066</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1740404072066</updated>
    </task>
    <task id="LOCAL-00017" summary="chore(hnyiti-module-message): 删除消息模块配置文件&#10;&#10;删除了 hnyiti-module-message 模块的 application.yml 配置文件。该文件包含了服务器端口、数据源配置、ShardingSphere 配置、MyBatis Plus 配置、Swagger 配置、日志配置、短信配置、微信配置以及 hnyiti 框架配置等多个方面的设置。&#10;&#10;移除这个配置文件可能会对以下功能产生影响：&#10;- 服务器端口设置- 数据库连接和分片配置&#10;- MyBatis Plus框架配置&#10;- Swagger API 文档配置&#10;- 日志级别设置&#10;- 短信和微信服务集成配置">
      <option name="closed" value="true" />
      <created>1740407759536</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1740407759536</updated>
    </task>
    <task id="LOCAL-00018" summary="refactor(hnyiti-module-message-biz): 移除 Seata相关配置和依赖&#10;&#10;-从 bootstrap.yml 中删除了 Seata 共享配置&#10;- 从 pom.xml 中移除了 Seata相关的多个依赖，包括：  - shardingsphere-transaction-base-seata-at&#10;  - seata-spring-boot-starter&#10;  - spring-cloud-starter-alibaba-seata&#10;- 此次修改旨在减少项目对 Seata 的依赖，可能影响分布式事务处理功能">
      <option name="closed" value="true" />
      <created>1740411838900</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1740411838900</updated>
    </task>
    <task id="LOCAL-00019" summary="controller 添加swagger注解">
      <option name="closed" value="true" />
      <created>1742902847904</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1742902847904</updated>
    </task>
    <task id="LOCAL-00020" summary="kdniao渠道配置">
      <option name="closed" value="true" />
      <created>1744078960313</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1744078960313</updated>
    </task>
    <task id="LOCAL-00021" summary="kdniao渠道配置">
      <option name="closed" value="true" />
      <created>1744087071975</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1744087071975</updated>
    </task>
    <task id="LOCAL-00022" summary="kdniao渠道配置">
      <option name="closed" value="true" />
      <created>1744087472365</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1744087472365</updated>
    </task>
    <task id="LOCAL-00023" summary="kdniao渠道配置">
      <option name="closed" value="true" />
      <created>1744107304546</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1744107304546</updated>
    </task>
    <task id="LOCAL-00024" summary="kdniao渠道配置">
      <option name="closed" value="true" />
      <created>1744110021938</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1744110021938</updated>
    </task>
    <task id="LOCAL-00025" summary="快递鸟自测问题修改">
      <option name="closed" value="true" />
      <created>1744128801855</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1744128801855</updated>
    </task>
    <task id="LOCAL-00026" summary="快递鸟自测问题修改">
      <option name="closed" value="true" />
      <created>1744200546952</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1744200546952</updated>
    </task>
    <task id="LOCAL-00027" summary="快递鸟自测问题修改">
      <option name="closed" value="true" />
      <created>1744271396835</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1744271396835</updated>
    </task>
    <task id="LOCAL-00028" summary="同步订单信息">
      <option name="closed" value="true" />
      <created>1745155582867</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1745155582867</updated>
    </task>
    <task id="LOCAL-00029" summary="同步订单信息">
      <option name="closed" value="true" />
      <created>1745155593401</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1745155593401</updated>
    </task>
    <task id="LOCAL-00030" summary="同步订单信息">
      <option name="closed" value="true" />
      <created>1745155600249</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1745155600249</updated>
    </task>
    <task id="LOCAL-00031" summary="refactor(hnyiti-module-pgsql): 重构配置文件和数据源配置&#10;&#10;- 简化 bootstrap.yaml 配置文件结构- 更新 Nacos 配置方式&#10;- 修改日志文件路径配置&#10;- 重构 PgsqlDataSourceConfig 中的数据源配置方式">
      <option name="closed" value="true" />
      <created>1745214321127</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1745214321128</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(module-pgsql): 实现订单同步到 PostgreSQL 数据库功能- 新增 KuaiDiInput接口和 OrderConsumer 类，实现订单同步消息消费&#10;- 重构 Jackson 配置，优化日期时间序列化和反序列化&#10;- 更新数据源配置和事务管理- 调整项目结构，增加新包和类">
      <option name="closed" value="true" />
      <created>1745215692522</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1745215692522</updated>
    </task>
    <task id="LOCAL-00033" summary="feat(module-pgsql): 新增订单费用相关数据结构和接口- 新增 DeliveryOrderFeeSnapshot、DeliveryOrderFeeSuper、DeliveryOrderFeeTenant 和 DeliveryOrderFeeUser 四个实体类&#10;- 新增对应的 Repository 接口&#10;- 在 DeliveryOrderService 中添加与费用相关的方法&#10;- 更新 OrderAllVo，增加费用快照、总部费用、代理商费用和用户费用等字段">
      <option name="closed" value="true" />
      <created>1745220864317</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1745220864317</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(pgsql): 添加多租户支持和安全配置&#10;&#10;- 在 ApiConstants 中添加 ID 常量&#10;- 在 OrderConsumer 中使用 TenantContextHolder 设置租户 ID&#10;- 更新 pom.xml，添加 hnyiti-spring-boot-starter-biz-tenant 依赖&#10;- 新增 SecurityConfiguration 类，配置 Spring Security&#10;- 新增 TenantIdInterceptor 类，实现租户 ID 的 Feign 请求拦截">
      <option name="closed" value="true" />
      <created>1745236749535</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1745236749535</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(module-pgsql): 更新数据源配置并集成 Seata&#10;&#10;- 新增 PostgreSQL 和 RocketMQ 配置&#10;- 更新数据源配置方式，排除 Druid 自动配置&#10;- 集成 Seata 分布式事务支持&#10;- 调整 MyBatis 配置- 更新和删除部分 Mapper 接口&#10;- 重命名部分类和文件">
      <option name="closed" value="true" />
      <created>1746890349145</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1746890349145</updated>
    </task>
    <task id="LOCAL-00036" summary="refactor(hnyiti-module-pgsql): 优化数据类型和逻辑删除配置&#10;&#10;- 将订单类型字段从 String 改为 Long，提高数据一致性和性能&#10;- 修改 MyBatis Plus 逻辑删除配置，使用布尔值替代整数- 移除未使用的注释代码，提高代码可读性">
      <option name="closed" value="true" />
      <created>1746891473330</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1746891473330</updated>
    </task>
    <task id="LOCAL-00037" summary="feat(module-pgsql): 添加数据库类型枚举和方言工厂&#10;&#10;- 新增 DbType 枚举类，定义了多种数据库类型&#10;- 实现 DialectFactory 类，用于获取不同数据库的方言对象&#10;-支持的数据库包括 MySQL、Oracle、PostgreSQL、SQL Server等多种类型&#10;- 为后续的分页查询等功能提供基础支持">
      <option name="closed" value="true" />
      <created>1747047033433</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1747047033433</updated>
    </task>
    <task id="LOCAL-00038" summary="feat(module-pgsql): 添加 P6Spy 数据库操作监控&#10;&#10;- 在 application.yaml 中配置 P6Spy 日志格式和自定义日志类&#10;- 在 pom.xml 中添加 p6spy-spring-boot-starter 依赖&#10;- 在 PgsqlOrderController 中注入 DeliveryOrderService，为后续功能扩展做准备">
      <option name="closed" value="true" />
      <created>1747053280875</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1747053280875</updated>
    </task>
    <task id="LOCAL-00039" summary="feat(module-pgsql): 优化数据源配置和表名注解&#10;&#10;- 在 application.yaml 中添加 common.datasource.pgsql.schema 配置项&#10;- 更新 datasource.url 以包含 schema 参数&#10;- 移除所有实体类中的 schema 属性&#10;- 更新 bootstrap.yaml 中的 seata配置文件名&#10;- 添加 Kryo 相关依赖">
      <option name="closed" value="true" />
      <created>1747058522680</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1747058522680</updated>
    </task>
    <task id="LOCAL-00040" summary="refactor(hnyiti-module-pgsql): 重构配置文件布局- 删除了 application.yaml 文件，将其内容合并到 bootstrap.yaml&#10;- 在 bootstrap.yaml 中添加了新的配置项，包括 main、servlet、mvc、jackson、cache 等&#10;- 优化了配置文件结构，提高了可读性和可维护性">
      <option name="closed" value="true" />
      <created>1747060084771</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1747060084771</updated>
    </task>
    <task id="LOCAL-00041" summary="refactor(system): 优化 OAuth2 访问令牌查询和删除逻辑&#10;&#10;- 新增删除访问令牌的专用方法 deleteByIdAtOnce&#10;- 新增查询包括已删除令牌的 selectListIncludeDeleted 方法&#10;- 修改 getUserTokenList 方法使用新的查询方法&#10;- 优化令牌删除逻辑，使用新的删除方法">
      <option name="closed" value="true" />
      <created>1747279756067</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1747279756067</updated>
    </task>
    <task id="LOCAL-00042" summary="fix(system): 修改密码时删除用户 token&#10;&#10;- 在 AdminUserServiceImpl 中添加删除用户 token 的逻辑&#10;- 在 OAuth2TokenService 接口中新增 removeAccessTokenByUserIdType 方法- 在 OAuth2TokenServiceImpl 中实现 removeAccessTokenByUserIdType 方法&#10;- 优化 TokenAuthenticationFilter 中的逻辑判断">
      <option name="closed" value="true" />
      <created>1747464868186</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1747464868186</updated>
    </task>
    <task id="LOCAL-00043" summary="feat(system): 允许跨域访问认证相关接口&#10;&#10;- 在 application.yaml 中添加了允许跨域访问的接口路径&#10;- 在 AdminAuthServiceImpl 中添加了获取 tenantId 的注释（实际代码未实现）- 移除了 AdminAuthServiceImpl 中的注释代码">
      <option name="closed" value="true" />
      <created>1747574273886</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1747574273886</updated>
    </task>
    <task id="LOCAL-00044" summary="refactor(system): 优化管理员登录逻辑并增加手机号相关校验- 新增通过手机号登录的认证方法 authenticateByMobile&#10;-增加手机号存在性唯一校验，避免重复手机号问题&#10;-优化登录流程，支持手机号和用户名两种登录方式&#10;- 修复潜在的安全问题，如用户不存在时抛出异常">
      <option name="closed" value="true" />
      <created>1747576515003</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1747576515003</updated>
    </task>
    <task id="LOCAL-00045" summary="feat(auth): 添加多租户支持并优化登录功能&#10;&#10;- 在登录时设置租户 ID&#10;- 在登录响应中返回租户 ID- 在 OAuth2 访问令牌中移除不必要的字段&#10;-优化网关中的用户缓存逻辑">
      <option name="closed" value="true" />
      <created>1747618141357</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1747618141357</updated>
    </task>
    <task id="LOCAL-00046" summary="fix(system): 修复 admin 用户登录导致的 SQL 注入和数据泄露问题&#10;&#10;- 在登录前清除租户上下文，避免数据泄露- 更新登录日志配置，忽略 system_users 表的操作记录">
      <option name="closed" value="true" />
      <created>1747635251810</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1747635251810</updated>
    </task>
    <option name="localTasksCounter" value="47" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="c262ce0b-9e76-4f3d-93b8-e99340f104ee" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="c262ce0b-9e76-4f3d-93b8-e99340f104ee">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="refactor(monitor): 重构服务状态变更通知功能&#10;&#10;- 移除了 WxCpRobotProperties 类，直接使用配置属性&#10;- 优化了服务状态变更通知的逻辑，支持多种状态&#10;- 添加了对服务下线事件的处理&#10;- 改进了通知消息的格式，使用 Markdown 格式&#10;-集成了 Hutool 工具库，用于 HTTP 请求和 JSON 处理" />
    <MESSAGE value="refactor: 移除 WxCpRobotService 类&#10;&#10;删除了 hnyiti-monitor 项目中的 WxCpRobotService 类。这个类提供了向企业微信机器人发送消息的功能，包括随机选择 webhook key 实现负载均衡。删除这个类可能是因为不再需要这个功能或者有其他替代方案。" />
    <MESSAGE value="build(hnyiti-monitor): 移除 Arthas 相关代码&#10;&#10;- 删除了安装 Arthas 的步骤&#10;- 移除了 Arthas 相关的端口暴露&#10;- 添加了直接启动应用的 ENTRYPOINT 指令" />
    <MESSAGE value="chore(hnyiti-module-message): 删除消息模块配置文件&#10;&#10;删除了 hnyiti-module-message 模块的 application.yml 配置文件。该文件包含了服务器端口、数据源配置、ShardingSphere 配置、MyBatis Plus 配置、Swagger 配置、日志配置、短信配置、微信配置以及 hnyiti 框架配置等多个方面的设置。&#10;&#10;移除这个配置文件可能会对以下功能产生影响：&#10;- 服务器端口设置- 数据库连接和分片配置&#10;- MyBatis Plus框架配置&#10;- Swagger API 文档配置&#10;- 日志级别设置&#10;- 短信和微信服务集成配置" />
    <MESSAGE value="refactor(hnyiti-module-message-biz): 移除 Seata相关配置和依赖&#10;&#10;-从 bootstrap.yml 中删除了 Seata 共享配置&#10;- 从 pom.xml 中移除了 Seata相关的多个依赖，包括：  - shardingsphere-transaction-base-seata-at&#10;  - seata-spring-boot-starter&#10;  - spring-cloud-starter-alibaba-seata&#10;- 此次修改旨在减少项目对 Seata 的依赖，可能影响分布式事务处理功能" />
    <MESSAGE value="controller 添加swagger注解" />
    <MESSAGE value="kdniao渠道配置" />
    <MESSAGE value="快递鸟自测问题修改" />
    <MESSAGE value="同步订单信息" />
    <MESSAGE value="refactor(hnyiti-module-pgsql): 重构配置文件和数据源配置&#10;&#10;- 简化 bootstrap.yaml 配置文件结构- 更新 Nacos 配置方式&#10;- 修改日志文件路径配置&#10;- 重构 PgsqlDataSourceConfig 中的数据源配置方式" />
    <MESSAGE value="feat(module-pgsql): 实现订单同步到 PostgreSQL 数据库功能- 新增 KuaiDiInput接口和 OrderConsumer 类，实现订单同步消息消费&#10;- 重构 Jackson 配置，优化日期时间序列化和反序列化&#10;- 更新数据源配置和事务管理- 调整项目结构，增加新包和类" />
    <MESSAGE value="feat(module-pgsql): 新增订单费用相关数据结构和接口- 新增 DeliveryOrderFeeSnapshot、DeliveryOrderFeeSuper、DeliveryOrderFeeTenant 和 DeliveryOrderFeeUser 四个实体类&#10;- 新增对应的 Repository 接口&#10;- 在 DeliveryOrderService 中添加与费用相关的方法&#10;- 更新 OrderAllVo，增加费用快照、总部费用、代理商费用和用户费用等字段" />
    <MESSAGE value="feat(pgsql): 添加多租户支持和安全配置&#10;&#10;- 在 ApiConstants 中添加 ID 常量&#10;- 在 OrderConsumer 中使用 TenantContextHolder 设置租户 ID&#10;- 更新 pom.xml，添加 hnyiti-spring-boot-starter-biz-tenant 依赖&#10;- 新增 SecurityConfiguration 类，配置 Spring Security&#10;- 新增 TenantIdInterceptor 类，实现租户 ID 的 Feign 请求拦截" />
    <MESSAGE value="feat(module-pgsql): 更新数据源配置并集成 Seata&#10;&#10;- 新增 PostgreSQL 和 RocketMQ 配置&#10;- 更新数据源配置方式，排除 Druid 自动配置&#10;- 集成 Seata 分布式事务支持&#10;- 调整 MyBatis 配置- 更新和删除部分 Mapper 接口&#10;- 重命名部分类和文件" />
    <MESSAGE value="refactor(hnyiti-module-pgsql): 优化数据类型和逻辑删除配置&#10;&#10;- 将订单类型字段从 String 改为 Long，提高数据一致性和性能&#10;- 修改 MyBatis Plus 逻辑删除配置，使用布尔值替代整数- 移除未使用的注释代码，提高代码可读性" />
    <MESSAGE value="feat(module-pgsql): 添加数据库类型枚举和方言工厂&#10;&#10;- 新增 DbType 枚举类，定义了多种数据库类型&#10;- 实现 DialectFactory 类，用于获取不同数据库的方言对象&#10;-支持的数据库包括 MySQL、Oracle、PostgreSQL、SQL Server等多种类型&#10;- 为后续的分页查询等功能提供基础支持" />
    <MESSAGE value="feat(module-pgsql): 添加 P6Spy 数据库操作监控&#10;&#10;- 在 application.yaml 中配置 P6Spy 日志格式和自定义日志类&#10;- 在 pom.xml 中添加 p6spy-spring-boot-starter 依赖&#10;- 在 PgsqlOrderController 中注入 DeliveryOrderService，为后续功能扩展做准备" />
    <MESSAGE value="feat(module-pgsql): 优化数据源配置和表名注解&#10;&#10;- 在 application.yaml 中添加 common.datasource.pgsql.schema 配置项&#10;- 更新 datasource.url 以包含 schema 参数&#10;- 移除所有实体类中的 schema 属性&#10;- 更新 bootstrap.yaml 中的 seata配置文件名&#10;- 添加 Kryo 相关依赖" />
    <MESSAGE value="refactor(hnyiti-module-pgsql): 重构配置文件布局- 删除了 application.yaml 文件，将其内容合并到 bootstrap.yaml&#10;- 在 bootstrap.yaml 中添加了新的配置项，包括 main、servlet、mvc、jackson、cache 等&#10;- 优化了配置文件结构，提高了可读性和可维护性" />
    <MESSAGE value="refactor(system): 优化 OAuth2 访问令牌查询和删除逻辑&#10;&#10;- 新增删除访问令牌的专用方法 deleteByIdAtOnce&#10;- 新增查询包括已删除令牌的 selectListIncludeDeleted 方法&#10;- 修改 getUserTokenList 方法使用新的查询方法&#10;- 优化令牌删除逻辑，使用新的删除方法" />
    <MESSAGE value="fix(system): 修改密码时删除用户 token&#10;&#10;- 在 AdminUserServiceImpl 中添加删除用户 token 的逻辑&#10;- 在 OAuth2TokenService 接口中新增 removeAccessTokenByUserIdType 方法- 在 OAuth2TokenServiceImpl 中实现 removeAccessTokenByUserIdType 方法&#10;- 优化 TokenAuthenticationFilter 中的逻辑判断" />
    <MESSAGE value="feat(system): 允许跨域访问认证相关接口&#10;&#10;- 在 application.yaml 中添加了允许跨域访问的接口路径&#10;- 在 AdminAuthServiceImpl 中添加了获取 tenantId 的注释（实际代码未实现）- 移除了 AdminAuthServiceImpl 中的注释代码" />
    <MESSAGE value="refactor(system): 优化管理员登录逻辑并增加手机号相关校验- 新增通过手机号登录的认证方法 authenticateByMobile&#10;-增加手机号存在性唯一校验，避免重复手机号问题&#10;-优化登录流程，支持手机号和用户名两种登录方式&#10;- 修复潜在的安全问题，如用户不存在时抛出异常" />
    <MESSAGE value="feat(auth): 添加多租户支持并优化登录功能&#10;&#10;- 在登录时设置租户 ID&#10;- 在登录响应中返回租户 ID- 在 OAuth2 访问令牌中移除不必要的字段&#10;-优化网关中的用户缓存逻辑" />
    <MESSAGE value="fix(system): 修复 admin 用户登录导致的 SQL 注入和数据泄露问题&#10;&#10;- 在登录前清除租户上下文，避免数据泄露- 更新登录日志配置，忽略 system_users 表的操作记录" />
    <option name="LAST_COMMIT_MESSAGE" value="fix(system): 修复 admin 用户登录导致的 SQL 注入和数据泄露问题&#10;&#10;- 在登录前清除租户上下文，避免数据泄露- 更新登录日志配置，忽略 system_users 表的操作记录" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="REARRANGE_BEFORE_PROJECT_COMMIT" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-module-message/hnyiti-module-message-biz/src/main/java/com/hnyiti/cloud/module/message/controller/admin/message/MessageController.java</url>
          <line>28</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/apache/shardingsphere/shardingsphere-sharding-core/5.1.1/shardingsphere-sharding-core-5.1.1-sources.jar!/org/apache/shardingsphere/sharding/route/engine/type/standard/ShardingStandardRoutingEngine.java</url>
          <line>171</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/com/github/binarywang/weixin-java-cp/4.6.0/weixin-java-cp-4.6.0.jar!/me/chanjar/weixin/cp/api/impl/WxCpGroupRobotServiceImpl.class</url>
          <line>58</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-kuaidi/src/main/java/com/hnyiti/kuaidi/kdniao/http/KdniaoRequest.java</url>
          <line>887</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-module-drp/hnyiti-module-drp-kuaidi/hnyiti-module-drp-kuaidi-biz/src/main/java/com/hnyiti/kuaidi/module/delivery/service/order/OrderServiceImpl.java</url>
          <line>2185</line>
          <option name="timeStamp" value="59" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-extension/*******/mybatis-plus-extension-*******.jar!/com/baomidou/mybatisplus/extension/plugins/pagination/DialectFactory.class</url>
          <line>65</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-module-pgsql/hnyiti-module-pgsql-biz/src/main/java/com/hnyiti/kuaidi/module/pgsql/dal/handler/PostgreSQLTimestamptzTypeHandler.java</url>
          <line>38</line>
          <option name="timeStamp" value="73" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-module-pgsql/hnyiti-module-pgsql-biz/src/main/java/com/hnyiti/kuaidi/module/pgsql/dal/handler/PostgreSQLTimestamptzTypeHandler.java</url>
          <line>43</line>
          <option name="timeStamp" value="74" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/io/seata/seata-all/1.7.0/seata-all-1.7.0-sources.jar!/io/seata/rm/datasource/sql/struct/cache/AbstractTableMetaCache.java</url>
          <line>90</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/hnyiti-framework/hnyiti-spring-boot-starter-biz-sms/src/main/java/com/hnyiti/kuaidi/framework/sms/core/client/impl/aliyun/AliyunSmsCodeMapping.java</url>
          <line>19</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <breakpoint type="java-exception">
          <properties class="java.time.format.DateTimeParseException" package="java.time.format" />
          <option name="timeStamp" value="4" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.google.common.cache.LocalCache$WriteThroughEntry" memberName="value" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>