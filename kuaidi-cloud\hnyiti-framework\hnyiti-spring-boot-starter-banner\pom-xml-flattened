<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.hnyiti.cloud</groupId>
    <artifactId>hnyiti-framework</artifactId>
    <version>2.0.0</version>
  </parent>
  <groupId>com.hnyiti.cloud</groupId>
  <artifactId>hnyiti-spring-boot-starter-banner</artifactId>
  <version>2.0.0</version>
  <name>${project.artifactId}</name>
  <description>Banner 用于在 console 控制台，打印开发文档、接口文档等</description>
  <dependencies>
    <dependency>
      <groupId>com.hnyiti.cloud</groupId>
      <artifactId>hnyiti-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
  </dependencies>
</project>
