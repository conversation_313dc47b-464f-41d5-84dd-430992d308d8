package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hnyiti.kuaidi.framework.common.core.KeyValue;
import com.hnyiti.kuaidi.framework.sms.core.client.SmsCommonResult;
import com.hnyiti.kuaidi.framework.sms.core.client.dto.SmsReceiveRespDTO;
import com.hnyiti.kuaidi.framework.sms.core.client.dto.SmsSendRespDTO;
import com.hnyiti.kuaidi.framework.sms.core.client.dto.SmsTemplateRespDTO;
import com.hnyiti.kuaidi.framework.sms.core.client.impl.AbstractSmsClient;
import com.hnyiti.kuaidi.framework.sms.core.property.SmsChannelProperties;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 通讯云短信功能实现
 *
 * <AUTHOR>
 */
@Slf4j
public class TongXunYunSmsClient extends AbstractSmsClient {

    private static final String SEND_URL = "http://www.dh3t.com/json/sms/Submit";

    private TongXunYunSmsChannelProperties tongXunYunProperties;

    public TongXunYunSmsClient(SmsChannelProperties properties) {
        super(properties, new TongXunYunCodeMapping());
    }

    @Override
    protected void doInit() {
        this.tongXunYunProperties = TongXunYunSmsChannelProperties.build(properties);
    }

    @Override
    protected SmsCommonResult<SmsSendRespDTO> doSendSms(Long sendLogId,
                                                        String mobile,
                                                        String apiTemplateId,
                                                        List<KeyValue<String, Object>> templateParams) throws Throwable {

        // 构建请求参数
        Map<String, Object> requestBody = buildRequestBody(mobile, templateParams);

        // 发送HTTP请求
        HttpResponse response = HttpRequest.post(SEND_URL)
                .header("Content-Type", "application/json; charset=UTF-8")
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();

        log.info("[doSendSms][发送短信] sendLogId({}) mobile({}) response({})",
                sendLogId, mobile, response.body());

        // 解析响应
        if (response.getStatus() != 200) {
            return SmsCommonResult.build(String.valueOf(response.getStatus()),
                    response.body(), null, null, new TongXunYunCodeMapping());
        }

        JSONObject responseJson = JSONUtil.parseObj(response.body());
        String result = responseJson.getStr("result");
        String desc = responseJson.getStr("desc");
        String msgid = responseJson.getStr("msgid");
        String failPhones = responseJson.getStr("failPhones");

        // 构建响应DTO
        SmsSendRespDTO respDTO = new SmsSendRespDTO();
        respDTO.setSerialNo(msgid);

        return SmsCommonResult.build(result, desc, null, respDTO, new TongXunYunCodeMapping());
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(String mobile, List<KeyValue<String, Object>> templateParams) {
        Map<String, Object> requestBody = new HashMap<>();

        // 基础参数
        requestBody.put("account", tongXunYunProperties.getAccount());
        requestBody.put("password", tongXunYunProperties.getPassword());
        requestBody.put("phones", mobile);
        requestBody.put("msgid", UUID.randomUUID().toString().replace("-", ""));

        // 短信签名
        if (StrUtil.isNotBlank(properties.getSignature())) {
            requestBody.put("sign", "【" + properties.getSignature() + "】");
        }

        // 短信内容
        String content = extractContent(templateParams);
        requestBody.put("content", content);

        return requestBody;
    }

    /**
     * 从模板参数中提取短信内容
     */
    private String extractContent(List<KeyValue<String, Object>> templateParams) {
        if (CollUtil.isEmpty(templateParams)) {
            return "";
        }

        for (KeyValue<String, Object> param : templateParams) {
            if ("content".equals(param.getKey())) {
                return String.valueOf(param.getValue());
            }
        }

        return "";
    }

    @Override
    protected List<SmsReceiveRespDTO> doParseSmsReceiveStatus(String text) throws Throwable {
        // 通讯云的状态报告解析，根据实际需要实现
        return null;
    }

    @Override
    protected SmsCommonResult<SmsTemplateRespDTO> doGetSmsTemplate(String apiTemplateId) throws Throwable {
        // 通讯云的模板获取，根据实际需要实现
        SmsCommonResult<SmsTemplateRespDTO> result = SmsCommonResult.build(null, null,
                null, null, new TongXunYunCodeMapping());
        return result;
    }
}
