# 通讯云短信客户端实现

## 概述

TongXunYunSmsClient 是基于通讯云短信 API 的短信发送客户端实现，支持发送验证码、通知类短信等功能。

## API 接口文档

- **接口地址**: http://www.dh3t.com/json/sms/Submit
- **请求方式**: POST
- **数据格式**: JSON
- **编码**: UTF-8

## 配置说明

### 基础配置

```java
SmsChannelProperties properties = new SmsChannelProperties();
properties.setId(1L);
properties.setSignature("您的签名");
properties.setCode(SmsChannelEnum.TONGXUNYUN.getCode());
properties.setApiKey("您的账号");
properties.setApiSecret("您的密码(MD5加密32位小写)");
```

### 配置参数说明

| 参数 | 必填 | 说明 |
|------|------|------|
| account | 是 | 通讯云提供的用户账号 |
| password | 是 | 密码，需采用MD5加密(32位小写) |
| signature | 否 | 短信签名，格式：【签名内容】 |

## 使用示例

### 基本使用

```java
// 1. 创建客户端
TongXunYunSmsClient smsClient = new TongXunYunSmsClient(properties);
smsClient.init();

// 2. 准备发送参数
Long sendLogId = System.currentTimeMillis();
String mobile = "***********";
String apiTemplateId = "template_id";

List<KeyValue<String, Object>> templateParams = new ArrayList<>();
templateParams.add(new KeyValue<>("content", "您的验证码是：123456"));

// 3. 发送短信
SmsCommonResult<SmsSendRespDTO> result = smsClient.sendSms(
    sendLogId, mobile, apiTemplateId, templateParams);

// 4. 处理结果
if (result.isSuccess()) {
    System.out.println("发送成功，序列号：" + result.getData().getSerialNo());
} else {
    System.out.println("发送失败：" + result.getMsg());
}
```

## 请求参数

| 参数名 | 必要性 | 说明 |
|--------|--------|------|
| account | 必填 | 用户账号 |
| password | 必填 | 密码，需采用MD5加密(32位小写) |
| phones | 必填 | 接收手机号码，多个手机号码用英文逗号分隔，最多1000个 |
| content | 必填 | 短信内容，最多1000个汉字 |
| msgid | 选填 | 该批短信编号，需保证唯一，不填的话响应里会给一个系统生成的短信编号 |
| sign | 选填 | 短信签名，示例如：【大汉三通】 |
| subcode | 选填 | 短信签名对应子码 |
| sendtime | 选填 | 定时发送时间，格式yyyyMMddHHmm |

## 响应参数

| 参数名 | 说明 |
|--------|------|
| msgid | 该批短信编号 |
| result | 该批短信提交结果，0表示成功 |
| desc | 状态描述 |
| failPhones | 如果提交的号码中含有错误号码将在此显示 |

## 错误码映射

| API错误码 | 框架错误码 | 说明 |
|-----------|------------|------|
| 0 | SUCCESS | 提交成功 |
| 1 | SMS_ACCOUNT_MONEY_NOT_ENOUGH | 账户余额不足 |
| 2 | SMS_TEMPLATE_INVALID | 模板无效 |
| 3 | SMS_MOBILE_INVALID | 手机号无效 |
| 其他 | SMS_UNKNOWN | 未知错误 |

## 注意事项

1. 密码必须使用MD5加密，32位小写格式
2. 短信内容不要出现【】[]这两种方括号，该字符为签名专用
3. 手机号码最多支持1000个，用英文逗号分隔
4. 短信内容最多1000个汉字
5. 国际号码格式为+国别号手机号，如：+***********

## 测试

运行测试类验证功能：

```bash
mvn test -Dtest=TongXunYunSmsClientTest
```

注意：集成测试需要配置真实的账号和密码。
